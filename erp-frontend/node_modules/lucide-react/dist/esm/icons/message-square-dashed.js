/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 17H7l-4 4v-7", key: "1r71xu" }],
  ["path", { d: "M14 17h1", key: "nufu4t" }],
  ["path", { d: "M14 3h1", key: "1ec4yj" }],
  ["path", { d: "M19 3a2 2 0 0 1 2 2", key: "18rm91" }],
  ["path", { d: "M21 14v1a2 2 0 0 1-2 2", key: "29akq3" }],
  ["path", { d: "M21 9v1", key: "mxsmne" }],
  ["path", { d: "M3 9v1", key: "1r0deq" }],
  ["path", { d: "M5 3a2 2 0 0 0-2 2", key: "y57alp" }],
  ["path", { d: "M9 3h1", key: "1yesri" }]
];
const MessageSquareDashed = createLucideIcon("message-square-dashed", __iconNode);

export { __iconNode, MessageSquareDashed as default };
//# sourceMappingURL=message-square-dashed.js.map
