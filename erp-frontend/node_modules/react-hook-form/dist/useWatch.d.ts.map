{"version": 3, "file": "useWatch.d.ts", "sourceRoot": "", "sources": ["../src/useWatch.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,OAAO,EACP,uBAAuB,EACvB,SAAS,EACT,cAAc,EACd,eAAe,EACf,WAAW,EAGZ,MAAM,SAAS,CAAC;AAIjB;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,kBAAkB,GAAG,YAAY,EACjC,KAAK,EAAE;IACP,YAAY,CAAC,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACrD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;AAC1C;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,UAAU,SAAS,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,EACpE,kBAAkB,GAAG,YAAY,EACjC,KAAK,EAAE;IACP,IAAI,EAAE,UAAU,CAAC;IACjB,YAAY,CAAC,EAAE,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACxD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB,GAAG,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AAC7C;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,WAAW,SACT,SAAS,SAAS,CAAC,YAAY,CAAC,EAAE,GAAG,SAAS,SAAS,CAAC,YAAY,CAAC,EAAE,EACzE,kBAAkB,GAAG,YAAY,EACjC,KAAK,EAAE;IACP,IAAI,EAAE,SAAS,CAAC,GAAG,WAAW,CAAC,CAAC;IAChC,YAAY,CAAC,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACrD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB,GAAG,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AAC/C;;;;;;;;;;;;GAYG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,KAC3C,uBAAuB,CAAC,YAAY,CAAC,CAAC"}