# نظام إدارة الموارد المؤسسية (ERP System)

نظام شامل لإدارة الموارد المؤسسية مبني بتقنيات حديثة مع دعم كامل للغة العربية ونظام RTL.

## المميزات الرئيسية

### 🎯 **إدارة شاملة**
- **لوحة تحكم متقدمة** مع إحصائيات في الوقت الفعلي
- **إدارة العملاء** مع تتبع تاريخ المعاملات
- **إدارة المخزون** مع تنبيهات المخزون المنخفض
- **نظام المبيعات** مع إدارة الفواتير والطلبات
- **تقارير تفاعلية** مع رسوم بيانية متقدمة
- **إعدادات شاملة** للنظام والمستخدمين

### 📱 **تصميم متجاوب**
- **دعم كامل للأجهزة المختلفة** (ديسكتوب، تابلت، موبايل)
- **جداول ذكية** تتحول إلى كروت في الشاشات الصغيرة
- **تنقل سهل** مع قائمة جانبية قابلة للطي
- **واجهة مستخدم حديثة** مع مكونات قابلة للإعادة

### 🌐 **دعم اللغة العربية**
- **RTL كامل** مع دعم الاتجاه من اليمين لليسار
- **خطوط عربية** محسنة للقراءة
- **تنسيق التواريخ والأرقام** حسب المعايير العربية
- **واجهة مترجمة بالكامل** للعربية

### 🛠 **تقنيات متقدمة**
- **Next.js 15** مع App Router
- **TypeScript** للأمان والموثوقية
- **Tailwind CSS** للتصميم السريع
- **Radix UI** للمكونات المتاحة
- **Recharts** للرسوم البيانية
- **React Hook Form** لإدارة النماذج

## التثبيت والتشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn أو pnpm

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd erp-frontend
```

2. **تثبيت المكتبات**
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **تشغيل السيرفر**
```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

4. **فتح المتصفح**
افتح [http://localhost:3000](http://localhost:3000) لرؤية النظام

## بنية المشروع

```
erp-frontend/
├── src/
│   ├── app/                    # صفحات النظام
│   │   ├── dashboard/          # لوحة التحكم
│   │   ├── customers/          # إدارة العملاء
│   │   ├── inventory/          # إدارة المخزون
│   │   ├── sales/              # المبيعات
│   │   ├── reports/            # التقارير
│   │   └── settings/           # الإعدادات
│   ├── components/             # المكونات القابلة للإعادة
│   │   ├── ui/                 # مكونات واجهة المستخدم
│   │   └── layout/             # مكونات التخطيط
│   └── lib/                    # المكتبات والأدوات المساعدة
├── public/                     # الملفات العامة
└── package.json               # إعدادات المشروع
```

## الصفحات المتاحة

### 📊 **لوحة التحكم** (`/dashboard`)
- إحصائيات المبيعات والعملاء
- تنبيهات النظام
- جدول الطلبات الأخيرة
- مؤشرات الأداء الرئيسية

### 👥 **إدارة العملاء** (`/customers`)
- قائمة العملاء مع البحث والفلترة
- إحصائيات العملاء
- تفاصيل كل عميل
- تاريخ المعاملات

### 📦 **إدارة المخزون** (`/inventory`)
- قائمة المنتجات
- تنبيهات المخزون المنخفض
- إحصائيات المخزون
- إدارة المواقع والموردين

### 💰 **المبيعات** (`/sales`)
- قائمة الفواتير
- إحصائيات المبيعات
- حالات الدفع
- مندوبي المبيعات

### 📈 **التقارير** (`/reports`)
- رسوم بيانية تفاعلية
- تقارير المبيعات الشهرية
- تحليل العملاء
- أداء المنتجات

### ⚙️ **الإعدادات** (`/settings`)
- إعدادات الشركة
- الملف الشخصي
- إعدادات الأمان
- تخصيص المظهر

## المكونات الرئيسية

### 🧩 **DataTable**
مكون جدول متقدم يدعم:
- البحث والفلترة
- الترتيب
- التصفح بالصفحات
- إخفاء/إظهار الأعمدة
- تصدير البيانات
- عرض متجاوب (جداول → كروت)

### 🎨 **UI Components**
مجموعة شاملة من مكونات واجهة المستخدم:
- Button, Card, Input
- Dropdown Menu, Dialog
- Table, Form Components
- Charts and Graphs

### 📱 **Layout System**
نظام تخطيط مرن يشمل:
- Header مع البحث والإشعارات
- Sidebar قابل للطي
- Main content area
- دعم RTL كامل

## التخصيص والتطوير

### 🎨 **تخصيص التصميم**
- تعديل الألوان في `globals.css`
- إضافة خطوط جديدة
- تخصيص مكونات UI

### 📊 **إضافة صفحات جديدة**
1. إنشاء مجلد جديد في `src/app/`
2. إضافة `page.tsx`
3. استخدام `MainLayout` للتخطيط
4. إضافة الرابط في `sidebar.tsx`

### 🔧 **إضافة مكونات جديدة**
1. إنشاء ملف في `src/components/ui/`
2. استخدام TypeScript للأمان
3. دعم RTL والتصميم المتجاوب
4. إضافة التوثيق

## الأمان والأداء

### 🔒 **الأمان**
- TypeScript للأمان في وقت التطوير
- Validation باستخدام Zod
- Sanitization للمدخلات
- إدارة الجلسات

### ⚡ **الأداء**
- Next.js App Router للتحميل السريع
- Code splitting تلقائي
- Image optimization
- CSS optimization

## المساهمة في المشروع

### 📝 **إرشادات المساهمة**
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

### 🧪 **الاختبار**
```bash
npm run test
npm run lint
npm run build
```

## الدعم والمساعدة

### 📚 **الموارد**
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Radix UI](https://www.radix-ui.com/)
- [TypeScript](https://www.typescriptlang.org/docs/)

### 🐛 **الإبلاغ عن المشاكل**
إذا واجهت أي مشاكل، يرجى إنشاء Issue في GitHub مع:
- وصف المشكلة
- خطوات إعادة الإنتاج
- لقطات شاشة إن أمكن
- معلومات البيئة (OS, Browser, etc.)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطويره بـ ❤️ باستخدام Next.js و TypeScript**
