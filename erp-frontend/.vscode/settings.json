{"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "css.validate": false, "less.validate": false, "scss.validate": false, "editor.quickSuggestions": {"strings": true}, "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.tabSize": 2, "editor.insertSpaces": true, "files.eol": "\n", "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true}}