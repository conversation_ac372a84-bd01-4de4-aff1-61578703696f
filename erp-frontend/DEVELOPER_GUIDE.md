# دليل المطور - نظام ERP

## إجابات على أسئلتك الأساسية

### 1. 🗂️ **التعامل مع الجداول الكبيرة والعريضة**

#### الحلول المطبقة:
- **مكون DataTable متقدم** في `src/components/ui/data-table.tsx`
- **Responsive Design**: الجداول تتحول لكروت في الموبايل
- **Virtual Scrolling**: للجداول الكبيرة جداً
- **Column Management**: إخفاء/إظهار الأعمدة حسب الحاجة

#### مثال الاستخدام:
```tsx
<DataTable
  data={largeDataset}
  columns={columns}
  pageSize={50}
  mobileCardRender={(item) => <CustomCard item={item} />}
/>
```

#### CSS للجداول المتجاوبة:
```css
.table-container {
  overflow-x: auto;
  max-width: 100%;
}

@media (max-width: 768px) {
  .desktop-table { display: none; }
  .mobile-card { display: block; }
}
```

### 2. 🎨 **توحيد تصميم لوحات التحكم**

#### نظام Design System المطبق:

**أ. Layout موحد:**
```tsx
// src/components/layout/main-layout.tsx
export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex flex-1 flex-col">
        <Header />
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
```

**ب. مكونات UI موحدة:**
- `Button`, `Card`, `Input` في `src/components/ui/`
- نظام ألوان موحد في `globals.css`
- Typography متسق

**ج. استخدام Layout في كل صفحة:**
```tsx
export default function DashboardPage() {
  return (
    <MainLayout>
      {/* محتوى الصفحة */}
    </MainLayout>
  )
}
```

### 3. 🖥️ **منع انقطاع السيرفر على Mac**

#### الحلول المطبقة:

**أ. استخدام nodemon (اختياري):**
```bash
npm install -g nodemon
nodemon --exec "npm run dev"
```

**ب. إعدادات Next.js محسنة:**
```javascript
// next.config.ts
const nextConfig = {
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
}
```

**ج. استخدام PM2 للإنتاج:**
```bash
npm install -g pm2
pm2 start npm --name "erp-system" -- run dev
pm2 save
pm2 startup
```

### 4. 🔄 **توحيد التصميم عبر المودولات**

#### استراتيجية Component Library:

**أ. مكونات أساسية قابلة للإعادة:**
```
src/components/ui/
├── button.tsx
├── card.tsx
├── input.tsx
├── table.tsx
└── data-table.tsx
```

**ب. Theme Provider:**
```tsx
// src/lib/theme-provider.tsx
export function ThemeProvider({ children }: ThemeProviderProps) {
  return (
    <div className="theme-provider" data-theme="default">
      {children}
    </div>
  )
}
```

**ج. CSS Variables للألوان:**
```css
:root {
  --primary: 221.2 83.2% 53.3%;
  --secondary: 210 40% 96%;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
}
```

### 5. 🌐 **دعم اللغة العربية (RTL)**

#### التطبيق الكامل:

**أ. HTML Direction:**
```tsx
// src/app/layout.tsx
<html lang="ar" dir="rtl">
```

**ب. CSS RTL:**
```css
[dir="rtl"] {
  direction: rtl;
}

/* تخصيص للعربية */
.text-right { text-align: right; }
.mr-2 { margin-right: 0.5rem; }
.ml-2 { margin-left: 0.5rem; }
```

**ج. خطوط عربية:**
```tsx
import { Cairo } from "next/font/google"

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  weight: ["200", "300", "400", "500", "600", "700", "800", "900"],
})
```

**د. تنسيق التواريخ والأرقام:**
```tsx
export function formatCurrency(amount: number) {
  return new Intl.NumberFormat("ar-EG", {
    style: "currency",
    currency: "EGP",
  }).format(amount)
}

export function formatDate(date: Date | string) {
  return new Intl.DateTimeFormat("ar-EG", {
    year: "numeric",
    month: "long", 
    day: "numeric",
  }).format(new Date(date))
}
```

### 6. 🛡️ **ضمان استقرار التصميم**

#### استراتيجيات الاستقرار:

**أ. CSS Grid & Flexbox:**
```css
.layout-grid {
  display: grid;
  grid-template-columns: 250px 1fr;
  min-height: 100vh;
}

.flex-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
```

**ب. Responsive Breakpoints:**
```css
/* Mobile First */
.container {
  width: 100%;
  padding: 1rem;
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
```

**ج. TypeScript للأمان:**
```tsx
interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  pageSize?: number
  className?: string
}
```

### 7. 🚀 **أفضل تقنية للتنفيذ**

#### المجموعة التقنية المختارة:

**أ. Next.js 15 + TypeScript:**
- App Router للأداء العالي
- Server Components
- Built-in optimization

**ب. Tailwind CSS:**
- Utility-first approach
- دعم RTL ممتاز
- تخصيص سهل

**ج. Radix UI + shadcn/ui:**
- مكونات accessible
- تخصيص كامل
- دعم RTL

**د. Recharts للرسوم البيانية:**
- مكتبة React native
- تخصيص كامل
- أداء عالي

### 8. 💡 **التنفيذ بدون خبرة برمجة**

#### استخدام Augment VS Code AI Agent:

**أ. الأوامر الأساسية:**
```
"أضف صفحة جديدة لإدارة الموظفين"
"عدل تصميم الجدول ليكون أكثر جاذبية"
"أضف رسم بياني للمبيعات الشهرية"
"غير لون الأزرار إلى الأخضر"
```

**ب. التخصيص السهل:**
- تعديل الألوان في `globals.css`
- إضافة بيانات جديدة في الملفات
- تغيير النصوص والترجمات

**ج. إضافة ميزات جديدة:**
```
"أضف نظام إشعارات"
"أنشئ صفحة تقارير مالية"
"أضف نظام صلاحيات المستخدمين"
```

## 🔧 **أوامر مفيدة للتطوير**

### تشغيل النظام:
```bash
cd erp-frontend
npm run dev
```

### بناء للإنتاج:
```bash
npm run build
npm start
```

### فحص الكود:
```bash
npm run lint
npm run type-check
```

### إضافة مكتبة جديدة:
```bash
npm install library-name
npm install -D @types/library-name
```

## 📱 **اختبار التصميم المتجاوب**

### في المتصفح:
1. افتح Developer Tools (F12)
2. اختر Device Toolbar
3. جرب أحجام مختلفة:
   - Mobile: 375px
   - Tablet: 768px  
   - Desktop: 1024px+

### أحجام الشاشات المدعومة:
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px+
- **Large Desktop**: 1440px+

## 🎯 **نصائح للتطوير**

### 1. **استخدم المكونات الموجودة:**
```tsx
import { DataTable } from "@/components/ui/data-table"
import { Card } from "@/components/ui/card"
```

### 2. **اتبع نمط التسمية:**
```
- PascalCase للمكونات: `UserProfile`
- camelCase للمتغيرات: `userData`
- kebab-case للملفات: `user-profile.tsx`
```

### 3. **استخدم TypeScript:**
```tsx
interface User {
  id: string
  name: string
  email: string
}
```

### 4. **اختبر على أجهزة مختلفة:**
- Chrome DevTools
- Firefox Responsive Design
- Safari Web Inspector

هذا النظام مصمم ليكون قابلاً للتوسع والتخصيص بسهولة، مع دعم كامل للغة العربية والتصميم المتجاوب!
