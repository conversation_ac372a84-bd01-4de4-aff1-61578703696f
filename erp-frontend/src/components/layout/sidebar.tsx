"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Users,
  Package,
  ShoppingCart,
  FileText,
  BarChart3,
  Settings,
  Building2,
  Wallet,
  UserCheck,
  Truck,
  Calendar,
  MessageSquare,
} from "lucide-react"

const sidebarItems = [
  {
    title: "لوحة التحكم",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "إدارة العملاء",
    href: "/customers",
    icon: Users,
  },
  {
    title: "إدارة المخزون",
    href: "/inventory",
    icon: Package,
  },
  {
    title: "المبيعات",
    href: "/sales",
    icon: ShoppingCart,
  },
  {
    title: "المشتريات",
    href: "/purchases",
    icon: Truck,
  },
  {
    title: "الفواتير",
    href: "/invoices",
    icon: FileText,
  },
  {
    title: "الحسابات",
    href: "/accounts",
    icon: Wallet,
  },
  {
    title: "الموظفين",
    href: "/employees",
    icon: UserCheck,
  },
  {
    title: "المواعيد",
    href: "/appointments",
    icon: Calendar,
  },
  {
    title: "الرسائل",
    href: "/messages",
    icon: MessageSquare,
  },
  {
    title: "التقارير",
    href: "/reports",
    icon: BarChart3,
  },
  {
    title: "الشركة",
    href: "/company",
    icon: Building2,
  },
  {
    title: "الإعدادات",
    href: "/settings",
    icon: Settings,
  },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()

  return (
    <div className={cn("pb-12 min-h-screen", className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <div className="flex items-center mb-6">
            <Building2 className="h-8 w-8 ml-2" />
            <h2 className="text-lg font-semibold">نظام ERP</h2>
          </div>
          <div className="space-y-1">
            {sidebarItems.map((item) => (
              <Button
                key={item.href}
                variant={pathname === item.href ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  pathname === item.href && "bg-muted font-medium"
                )}
                asChild
              >
                <Link href={item.href}>
                  <item.icon className="ml-2 h-4 w-4" />
                  {item.title}
                </Link>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
