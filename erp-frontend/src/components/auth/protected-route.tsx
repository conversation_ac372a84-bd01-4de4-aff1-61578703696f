"use client"

import { useAuth } from '@/contexts/auth-context'
import { hasPermission } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, AlertTriangle, ArrowRight } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  module?: string
  action?: 'read' | 'write' | 'delete' | 'admin'
  fallback?: React.ReactNode
}

export function ProtectedRoute({ 
  children, 
  module, 
  action = 'read', 
  fallback 
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isLoading, isAuthenticated, router])

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    )
  }

  // Not authenticated
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-red-100 rounded-full">
                <Shield className="h-8 w-8 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-xl font-bold text-red-600">
              غير مصرح بالدخول
            </CardTitle>
            <CardDescription>
              يجب تسجيل الدخول للوصول إلى هذه الصفحة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push('/login')} 
              className="w-full"
            >
              <ArrowRight className="ml-2 h-4 w-4" />
              الذهاب لتسجيل الدخول
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Check module permissions
  if (module && !hasPermission(user, module, action)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-orange-100 rounded-full">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              </div>
            </div>
            <CardTitle className="text-xl font-bold text-orange-600">
              ليس لديك صلاحية
            </CardTitle>
            <CardDescription>
              ليس لديك صلاحية للوصول إلى هذا القسم
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-medium text-orange-900 mb-2">معلومات حسابك:</h4>
              <div className="text-sm text-orange-800 space-y-1">
                <div><strong>الاسم:</strong> {user.name}</div>
                <div><strong>الدور:</strong> {user.role}</div>
                <div><strong>القسم:</strong> {user.department}</div>
              </div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">الصلاحيات المتاحة:</h4>
              <div className="text-sm text-blue-800 space-y-1">
                {user.permissions.map((perm, index) => (
                  <div key={index}>
                    <strong>{perm.module}:</strong> {perm.actions.join(', ')}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex space-x-2">
              <Button 
                onClick={() => router.back()} 
                variant="outline"
                className="flex-1"
              >
                العودة
              </Button>
              <Button 
                onClick={() => router.push('/dashboard')} 
                className="flex-1"
              >
                لوحة التحكم
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return <>{children}</>
}

// مكون للتحقق من الصلاحيات داخل الصفحة
interface PermissionGuardProps {
  module: string
  action: 'read' | 'write' | 'delete' | 'admin'
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function PermissionGuard({ 
  module, 
  action, 
  children, 
  fallback 
}: PermissionGuardProps) {
  const { user } = useAuth()

  if (!user || !hasPermission(user, module, action)) {
    if (fallback) {
      return <>{fallback}</>
    }
    return null
  }

  return <>{children}</>
}
