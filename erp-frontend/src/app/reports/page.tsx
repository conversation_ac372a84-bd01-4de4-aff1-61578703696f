"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { formatCurrency } from "@/lib/utils"
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar,
  DollarSign,
  Users,
  Package,
  ShoppingCart
} from "lucide-react"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend
} from "recharts"

// Sample data for charts
const monthlySalesData = [
  { month: "يناير", sales: 125000, orders: 45 },
  { month: "فبراير", sales: 135000, orders: 52 },
  { month: "مارس", sales: 148000, orders: 61 },
  { month: "أبريل", sales: 142000, orders: 58 },
  { month: "مايو", sales: 156000, orders: 67 },
  { month: "يونيو", sales: 168000, orders: 72 },
]

const categoryData = [
  { name: "أجهزة كمبيوتر", value: 45, color: "#0088FE" },
  { name: "طابعات", value: 25, color: "#00C49F" },
  { name: "شاشات", value: 20, color: "#FFBB28" },
  { name: "ملحقات", value: 10, color: "#FF8042" },
]

const topProductsData = [
  { name: "لابتوب ديل XPS 13", sales: 25, revenue: 625000 },
  { name: "شاشة سامسونج 27\"", sales: 18, revenue: 75600 },
  { name: "طابعة HP LaserJet", sales: 12, revenue: 42000 },
  { name: "هارد ديسك خارجي", sales: 15, revenue: 18000 },
  { name: "ماوس لاسلكي", sales: 35, revenue: 11200 },
]

const customerGrowthData = [
  { month: "يناير", newCustomers: 12, totalCustomers: 150 },
  { month: "فبراير", newCustomers: 18, totalCustomers: 168 },
  { month: "مارس", newCustomers: 15, totalCustomers: 183 },
  { month: "أبريل", newCustomers: 22, totalCustomers: 205 },
  { month: "مايو", newCustomers: 19, totalCustomers: 224 },
  { month: "يونيو", newCustomers: 25, totalCustomers: 249 },
]

export default function ReportsPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">التقارير والإحصائيات</h1>
            <p className="text-muted-foreground">
              تحليل الأداء ومتابعة المؤشرات الرئيسية
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Calendar className="ml-2 h-4 w-4" />
              تخصيص الفترة
            </Button>
            <Button>
              <Download className="ml-2 h-4 w-4" />
              تصدير التقرير
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                إجمالي المبيعات
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(monthlySalesData.reduce((sum, item) => sum + item.sales, 0))}
              </div>
              <p className="text-xs text-muted-foreground flex items-center">
                <TrendingUp className="h-3 w-3 text-green-500 ml-1" />
                +12.5% من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                عدد الطلبات
              </CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {monthlySalesData.reduce((sum, item) => sum + item.orders, 0)}
              </div>
              <p className="text-xs text-muted-foreground flex items-center">
                <TrendingUp className="h-3 w-3 text-green-500 ml-1" />
                +8.2% من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                العملاء الجدد
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {customerGrowthData.reduce((sum, item) => sum + item.newCustomers, 0)}
              </div>
              <p className="text-xs text-muted-foreground flex items-center">
                <TrendingUp className="h-3 w-3 text-green-500 ml-1" />
                +15.3% من الشهر الماضي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                المنتجات المباعة
              </CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {topProductsData.reduce((sum, item) => sum + item.sales, 0)}
              </div>
              <p className="text-xs text-muted-foreground flex items-center">
                <TrendingUp className="h-3 w-3 text-green-500 ml-1" />
                +6.8% من الشهر الماضي
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row 1 */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Monthly Sales Chart */}
          <Card>
            <CardHeader>
              <CardTitle>المبيعات الشهرية</CardTitle>
              <CardDescription>
                تطور المبيعات خلال الأشهر الستة الماضية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlySalesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'sales' ? formatCurrency(Number(value)) : value,
                      name === 'sales' ? 'المبيعات' : 'الطلبات'
                    ]}
                  />
                  <Bar dataKey="sales" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Category Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>توزيع المبيعات حسب الفئة</CardTitle>
              <CardDescription>
                نسبة المبيعات لكل فئة منتج
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row 2 */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Customer Growth */}
          <Card>
            <CardHeader>
              <CardTitle>نمو قاعدة العملاء</CardTitle>
              <CardDescription>
                العملاء الجدد وإجمالي العملاء شهرياً
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={customerGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="newCustomers" 
                    stroke="#8884d8" 
                    name="عملاء جدد"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="totalCustomers" 
                    stroke="#82ca9d" 
                    name="إجمالي العملاء"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Top Products */}
          <Card>
            <CardHeader>
              <CardTitle>أفضل المنتجات مبيعاً</CardTitle>
              <CardDescription>
                المنتجات الأكثر مبيعاً وإيراداً
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={topProductsData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={120} />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'revenue' ? formatCurrency(Number(value)) : value,
                      name === 'revenue' ? 'الإيراد' : 'الكمية'
                    ]}
                  />
                  <Bar dataKey="sales" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Summary Table */}
        <Card>
          <CardHeader>
            <CardTitle>ملخص الأداء</CardTitle>
            <CardDescription>
              مؤشرات الأداء الرئيسية للفترة الحالية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <h4 className="font-medium">المبيعات</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>إجمالي المبيعات:</span>
                    <span className="font-medium">{formatCurrency(774000)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>متوسط قيمة الطلب:</span>
                    <span className="font-medium">{formatCurrency(2465)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>أعلى مبيعات شهرية:</span>
                    <span className="font-medium">{formatCurrency(168000)}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">العملاء</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>إجمالي العملاء:</span>
                    <span className="font-medium">249</span>
                  </div>
                  <div className="flex justify-between">
                    <span>عملاء جدد:</span>
                    <span className="font-medium">111</span>
                  </div>
                  <div className="flex justify-between">
                    <span>معدل النمو:</span>
                    <span className="font-medium text-green-600">+80.4%</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">المنتجات</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>إجمالي المنتجات المباعة:</span>
                    <span className="font-medium">105</span>
                  </div>
                  <div className="flex justify-between">
                    <span>أكثر المنتجات مبيعاً:</span>
                    <span className="font-medium">ماوس لاسلكي</span>
                  </div>
                  <div className="flex justify-between">
                    <span>أعلى إيراد:</span>
                    <span className="font-medium">لابتوب ديل</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
