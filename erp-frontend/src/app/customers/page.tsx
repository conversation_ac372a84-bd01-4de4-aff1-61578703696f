"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { Button } from "@/components/ui/button"
import { formatCurrency, formatDate } from "@/lib/utils"
import { Plus, Phone, Mail, MapPin, Edit, Trash2 } from "lucide-react"

// Sample customer data
const customers = [
  {
    id: "CUST-001",
    name: "أحمد محمد علي",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "شركة النور للتجارة",
    address: "القاهرة، مصر الجديدة",
    totalOrders: 15,
    totalSpent: 25430.50,
    lastOrder: "2024-01-15",
    status: "نشط",
    joinDate: "2023-06-15",
  },
  {
    id: "CUST-002",
    name: "فاطمة علي حسن",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "مؤسسة الأمل",
    address: "الجيزة، الدقي",
    totalOrders: 8,
    totalSpent: 12750.00,
    lastOrder: "2024-01-12",
    status: "نشط",
    joinDate: "2023-08-20",
  },
  {
    id: "CUST-003",
    name: "محمد حسن إبراهيم",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "شركة المستقبل",
    address: "الإسكندرية، سموحة",
    totalOrders: 22,
    totalSpent: 45200.75,
    lastOrder: "2024-01-14",
    status: "نشط",
    joinDate: "2023-03-10",
  },
  {
    id: "CUST-004",
    name: "سارة أحمد محمود",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "مكتب الإبداع",
    address: "القاهرة، المعادي",
    totalOrders: 5,
    totalSpent: 8500.25,
    lastOrder: "2024-01-08",
    status: "غير نشط",
    joinDate: "2023-11-05",
  },
  {
    id: "CUST-005",
    name: "عمر خالد يوسف",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "شركة التطوير الحديث",
    address: "الجيزة، المهندسين",
    totalOrders: 18,
    totalSpent: 32100.00,
    lastOrder: "2024-01-13",
    status: "نشط",
    joinDate: "2023-05-22",
  },
  {
    id: "CUST-006",
    name: "نورا سامي عبدالله",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "مؤسسة الرؤية",
    address: "القاهرة، النزهة",
    totalOrders: 12,
    totalSpent: 19800.50,
    lastOrder: "2024-01-11",
    status: "نشط",
    joinDate: "2023-07-18",
  },
  {
    id: "CUST-007",
    name: "حسام محمد طه",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "شركة الابتكار",
    address: "الإسكندرية، المنتزه",
    totalOrders: 9,
    totalSpent: 15600.75,
    lastOrder: "2024-01-09",
    status: "نشط",
    joinDate: "2023-09-12",
  },
  {
    id: "CUST-008",
    name: "مريم أحمد فتحي",
    email: "<EMAIL>",
    phone: "+20 ************",
    company: "مكتب الإنجاز",
    address: "القاهرة، مدينة نصر",
    totalOrders: 6,
    totalSpent: 11200.00,
    lastOrder: "2024-01-07",
    status: "غير نشط",
    joinDate: "2023-10-30",
  },
]

const customerColumns: Column<typeof customers[0]>[] = [
  {
    key: "id",
    title: "رقم العميل",
    sortable: true,
    width: "120px",
  },
  {
    key: "name",
    title: "الاسم",
    sortable: true,
    render: (value, row) => (
      <div>
        <div className="font-medium">{value}</div>
        <div className="text-sm text-muted-foreground">{row.company}</div>
      </div>
    ),
  },
  {
    key: "email",
    title: "البريد الإلكتروني",
    render: (value) => (
      <div className="flex items-center">
        <Mail className="h-4 w-4 ml-2 text-muted-foreground" />
        <span className="text-sm">{value}</span>
      </div>
    ),
  },
  {
    key: "phone",
    title: "الهاتف",
    render: (value) => (
      <div className="flex items-center">
        <Phone className="h-4 w-4 ml-2 text-muted-foreground" />
        <span className="text-sm">{value}</span>
      </div>
    ),
  },
  {
    key: "address",
    title: "العنوان",
    render: (value) => (
      <div className="flex items-center">
        <MapPin className="h-4 w-4 ml-2 text-muted-foreground" />
        <span className="text-sm">{value}</span>
      </div>
    ),
  },
  {
    key: "totalOrders",
    title: "عدد الطلبات",
    sortable: true,
    width: "120px",
  },
  {
    key: "totalSpent",
    title: "إجمالي المشتريات",
    sortable: true,
    render: (value) => formatCurrency(value),
    width: "150px",
  },
  {
    key: "lastOrder",
    title: "آخر طلب",
    sortable: true,
    render: (value) => formatDate(value),
    width: "120px",
  },
  {
    key: "status",
    title: "الحالة",
    render: (value) => (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === "نشط"
            ? "bg-green-100 text-green-800"
            : "bg-gray-100 text-gray-800"
        }`}
      >
        {value}
      </span>
    ),
    width: "100px",
  },
  {
    key: "id",
    title: "الإجراءات",
    render: (value, row) => (
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm">
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
    width: "120px",
  },
]

export default function CustomersPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">إدارة العملاء</h1>
            <p className="text-muted-foreground">
              إدارة بيانات العملاء ومتابعة طلباتهم
            </p>
          </div>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            إضافة عميل جديد
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                إجمالي العملاء
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{customers.length}</div>
              <p className="text-xs text-muted-foreground">
                +2 عميل جديد هذا الشهر
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                العملاء النشطون
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {customers.filter(c => c.status === "نشط").length}
              </div>
              <p className="text-xs text-muted-foreground">
                {Math.round((customers.filter(c => c.status === "نشط").length / customers.length) * 100)}% من إجمالي العملاء
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                متوسط الطلبات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(customers.reduce((sum, c) => sum + c.totalOrders, 0) / customers.length)}
              </div>
              <p className="text-xs text-muted-foreground">
                طلب لكل عميل
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                متوسط الإنفاق
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(customers.reduce((sum, c) => sum + c.totalSpent, 0) / customers.length)}
              </div>
              <p className="text-xs text-muted-foreground">
                لكل عميل
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Customers Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة العملاء</CardTitle>
            <CardDescription>
              جميع العملاء المسجلين في النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              data={customers}
              columns={customerColumns}
              pageSize={10}
              mobileCardRender={(customer) => (
                <div className="p-4 space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{customer.name}</div>
                      <div className="text-sm text-muted-foreground">{customer.company}</div>
                    </div>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        customer.status === "نشط"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {customer.status}
                    </span>
                  </div>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 ml-2 text-muted-foreground" />
                      {customer.email}
                    </div>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 ml-2 text-muted-foreground" />
                      {customer.phone}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 ml-2 text-muted-foreground" />
                      {customer.address}
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>الطلبات: {customer.totalOrders}</span>
                    <span>الإنفاق: {formatCurrency(customer.totalSpent)}</span>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="ml-2 h-4 w-4" />
                      تعديل
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Trash2 className="ml-2 h-4 w-4" />
                      حذف
                    </Button>
                  </div>
                </div>
              )}
            />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
